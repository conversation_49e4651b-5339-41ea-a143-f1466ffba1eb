defmodule MqttableWeb.Shared.MessageFormComponents do
  @moduledoc """
  Shared components for message forms used across different modals.

  This module provides reusable form components for MQTT message composition,
  including topic input, payload input, QoS selection, and other common fields.
  """

  use Phoenix.Component
  import MqttableWeb.CoreComponents

  @doc """
  Renders a client selection dropdown component.
  """
  attr :form, :map, required: true
  attr :connected_clients, :list, required: true
  attr :active_broker_name, :string, required: true
  attr :myself, :any, required: true
  attr :disabled, :boolean, default: false
  attr :label, :string, default: "Client"
  attr :required, :boolean, default: true

  def client_selection(assigns) do
    ~H"""
    <div class="form-control w-full">
      <!-- Hidden input to maintain form field for validation -->
      <input type="hidden" name="client_id" value={@form["client_id"]} />

      <div class="dropdown w-full">
        <div
          tabindex="0"
          role="button"
          class={[
            "btn btn-outline w-full justify-start",
            if(@form["client_id"] == "", do: "btn-ghost text-base-content/50", else: ""),
            if(@disabled, do: "btn-disabled", else: "")
          ]}
          disabled={@disabled}
        >
          <%= if @form["client_id"] == "" do %>
            Select a connected client
          <% else %>
            <.icon name="hero-wifi" class="size-4 mr-2" />
            {@form["client_id"]}
          <% end %>
          <.icon name="hero-chevron-down" class="size-4 ml-auto" />
        </div>
        <ul
          tabindex="0"
          class="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-base-300 max-h-60 overflow-y-auto"
        >
          <%= for client <- @connected_clients do %>
            <li>
              <button
                type="button"
                class="flex items-center justify-start w-full"
                phx-click="select_client"
                phx-value-client_id={client.client_id}
                phx-target={@myself}
              >
                <.icon name="hero-wifi" class="size-4 mr-2" />
                <span class="truncate">{client.client_id}</span>
              </button>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
    """
  end

  @doc """
  Renders a QoS level selection component.
  """
  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :label, :string, default: "QoS Level"

  def qos_selection(assigns) do
    # Ensure QoS defaults to "0" if not set or nil
    assigns =
      assign_new(assigns, :current_qos, fn ->
        case assigns.form["qos"] do
          nil -> "0"
          "" -> "0"
          value -> to_string(value)
        end
      end)

    ~H"""
    <div class="join">
      <%= for qos <- [0, 1, 2] do %>
        <input
          type="radio"
          name="qos"
          value={qos}
          checked={@current_qos == to_string(qos)}
          class="join-item btn btn-sm btn-outline"
          aria-label={"QoS #{qos}"}
        />
      <% end %>
    </div>
    """
  end

  @doc """
  Renders a retain message checkbox component.
  """
  attr :form, :map, required: true
  attr :label, :string, default: "Retain Message"

  def retain_checkbox(assigns) do
    ~H"""
    <div class="form-control w-full">
      <label class="label cursor-pointer justify-start">
        <input
          type="checkbox"
          name="retain"
          checked={@form["retain"] == true || @form["retain"] == "on"}
          class="toggle checked:bg-orange-500 mr-3"
        />
        <span class="label-text font-medium">{@label}</span>
      </label>
    </div>
    """
  end

  @doc """
  Renders the enhanced payload input using EnhancedPayloadEditorComponent.
  """
  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :label, :string, default: "Payload"
  attr :enable_templates, :boolean, default: true
  attr :active_broker_name, :string, default: nil

  def payload_input(assigns) do
    ~H"""
    <.live_component
      module={MqttableWeb.EnhancedPayloadEditorComponent}
      id={"enhanced-payload-editor-#{@myself}"}
      payload={@form["payload"] || ""}
      payload_format={@form["payload_format"] || "text"}
      label={@label}
    />
    """
  end

  @doc """
  Renders MQTT 5.0 properties section with full functionality.
  """
  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :collapsed, :boolean, default: true
  attr :show_properties, :boolean, default: false

  def mqtt5_properties_section(assigns) do
    ~H"""
    <%= if @show_properties do %>
      <div class="form-control w-full">
        <div class="collapse collapse-arrow bg-base-200 rounded-lg">
          <input
            type="checkbox"
            checked={!@collapsed}
            phx-click="toggle_mqtt5_properties"
            phx-target={@myself}
          />
          <div class="collapse-title text-sm font-medium flex items-center">
            <.icon name="hero-cog-6-tooth" class="size-4 mr-2" /> MQTT 5.0 Properties (Optional)
          </div>
          <div class="collapse-content">
            <div class="space-y-3 p-3">
              <!-- Compact Grid Layout for All Properties -->
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 text-sm">
                <!-- Content Type -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Content Type</span>
                  </label>
                  <input
                    type="text"
                    name="content_type"
                    value={@form["content_type"] || ""}
                    placeholder="application/json"
                    class="input input-bordered input-xs h-8"
                  />
                </div>
                
    <!-- Message Expiry -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Message Expiry (sec)</span>
                  </label>
                  <input
                    type="number"
                    name="message_expiry_interval"
                    value={@form["message_expiry_interval"] || ""}
                    placeholder="0"
                    min="0"
                    max="4294967295"
                    class="input input-bordered input-xs h-8"
                  />
                </div>
                
    <!-- Response Topic -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Response Topic</span>
                  </label>
                  <input
                    type="text"
                    name="response_topic"
                    value={@form["response_topic"] || ""}
                    placeholder="response/topic"
                    class="input input-bordered input-xs h-8"
                  />
                </div>
                
    <!-- Correlation Data -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Correlation Data</span>
                  </label>
                  <input
                    type="text"
                    name="correlation_data"
                    value={@form["correlation_data"] || ""}
                    placeholder="correlation-id-123"
                    class="input input-bordered input-xs h-8"
                  />
                </div>
                
    <!-- Topic Alias -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Topic Alias</span>
                  </label>
                  <input
                    type="number"
                    name="topic_alias"
                    value={@form["topic_alias"] || ""}
                    placeholder="0"
                    min="0"
                    max="65535"
                    class="input input-bordered input-xs h-8"
                  />
                </div>
                
    <!-- Payload Format Indicator -->
                <div class="form-control">
                  <label class="label py-1">
                    <span class="label-text text-xs font-medium">Payload Format Indicator</span>
                  </label>
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      name="payload_format_indicator"
                      checked={
                        @form["payload_format_indicator"] == true ||
                          @form["payload_format_indicator"] == "on"
                      }
                      class="toggle checked:bg-orange-500 mr-3"
                    />
                    <span class="label-text text-xs font-medium">UTF-8</span>
                  </div>
                </div>
              </div>
              <!-- User Properties Section -->
              <div class="border-t border-base-300 pt-3 mt-3">
                <div class="flex items-center justify-between mb-2">
                  <label class="label-text text-xs font-medium flex items-center">
                    <.icon name="hero-tag" class="size-3 mr-1" /> User Properties
                  </label>
                  <button
                    type="button"
                    class="btn btn-xs btn-outline btn-primary"
                    phx-click="add_user_property"
                    phx-target={@myself}
                  >
                    <.icon name="hero-plus" class="size-3 mr-1" /> Add
                  </button>
                </div>

                <%= if Enum.empty?(@form["user_properties"] || []) do %>
                  <div class="text-center py-2 text-base-content/50">
                    <p class="text-xs">No custom properties</p>
                  </div>
                <% else %>
                  <div class="space-y-2">
                    <%= for {user_prop, index} <- Enum.with_index(@form["user_properties"] || []) do %>
                      <div class="flex gap-2 items-center bg-base-200 p-2 rounded">
                        <input
                          type="text"
                          name={"user_property_key_#{index}"}
                          value={user_prop["key"] || ""}
                          placeholder="Key"
                          class="input input-bordered input-xs h-7 flex-1 text-xs"
                          phx-change="user_property_changed"
                          phx-target={@myself}
                        />
                        <input
                          type="text"
                          name={"user_property_value_#{index}"}
                          value={user_prop["value"] || ""}
                          placeholder="Value"
                          class="input input-bordered input-xs h-7 flex-1 text-xs"
                          phx-change="user_property_changed"
                          phx-target={@myself}
                        />
                        <button
                          type="button"
                          class="btn btn-xs btn-ghost btn-square text-error hover:bg-error hover:text-error-content"
                          phx-click="remove_user_property"
                          phx-value-index={index}
                          phx-target={@myself}
                          title="Remove"
                        >
                          <.icon name="hero-x-mark" class="size-3" />
                        </button>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    """
  end
end
